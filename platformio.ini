; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32-c3-devkitm-1]
platform = espressif32
board = esp32-c3-devkitm-1
framework = arduino
monitor_speed = 115200

; Library dependencies for thermal sensor project
lib_deps =
    https://github.com/adafruit/Adafruit_AMG88xx
    adafruit/Adafruit BusIO
    adafruit/Adafruit SSD1306
    adafruit/Adafruit GFX Library
