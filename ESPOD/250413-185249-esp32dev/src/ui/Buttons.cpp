#include "Buttons.h"
#include "driver/gpio.h"
#include "esp_timer.h"

// Pin assignments for 7-button joystick
// Using safe GPIO pins that don't conflict with existing usage
gpio_num_t buttonPins[BTN_COUNT] = {
    GPIO_NUM_13,  // BTN_UP
    GPIO_NUM_12,  // BTN_DOWN
    GPIO_NUM_14,  // BTN_LEFT
    GPIO_NUM_27,  // BTN_RIGHT
    GPIO_NUM_26,  // BTN_CENTER
    GPIO_NUM_25,  // BTN_SET
    GPIO_NUM_33   // BTN_RST
};

// Button state tracking
static bool buttonStates[BTN_COUNT] = {false};
static bool lastButtonStates[BTN_COUNT] = {false};
static uint32_t buttonPressTime[BTN_COUNT] = {0};
static bool longPressDetected[BTN_COUNT] = {false};

// Callbacks
static ButtonCallback buttonPressCallback = nullptr;
static LongPressCallback longPressCallback = nullptr;

// Timing constants
#define DEBOUNCE_TIME_MS 50
#define LONG_PRESS_TIME_MS 2000

// Task handle for button monitoring
static TaskHandle_t buttonTaskHandle = nullptr;

// Button monitoring task
void buttonMonitorTask(void* parameter) {
    while (true) {
        uint32_t currentTime = millis();

        for (int i = 0; i < BTN_COUNT; i++) {
            bool currentState = !digitalRead(buttonPins[i]); // Active low (button pressed = LOW)

            // Debouncing
            if (currentState != lastButtonStates[i]) {
                vTaskDelay(pdMS_TO_TICKS(DEBOUNCE_TIME_MS));
                currentState = !digitalRead(buttonPins[i]);

                if (currentState != lastButtonStates[i]) {
                    lastButtonStates[i] = currentState;

                    if (currentState) {
                        // Button pressed
                        buttonStates[i] = true;
                        buttonPressTime[i] = currentTime;
                        longPressDetected[i] = false;
                    } else {
                        // Button released
                        if (buttonStates[i] && !longPressDetected[i]) {
                            // Short press detected
                            Serial.printf("[BUTTON] Short press detected on button %d\n", i);
                            if (buttonPressCallback) {
                                buttonPressCallback((button_id_t)i);
                            }
                        }
                        buttonStates[i] = false;
                        longPressDetected[i] = false;
                    }
                }
            }

            // Check for long press
            if (buttonStates[i] && !longPressDetected[i]) {
                if (currentTime - buttonPressTime[i] >= LONG_PRESS_TIME_MS) {
                    longPressDetected[i] = true;
                    Serial.printf("[BUTTON] Long press detected on button %d\n", i);
                    if (longPressCallback) {
                        longPressCallback((button_id_t)i);
                    }
                }
            }
        }

        vTaskDelay(pdMS_TO_TICKS(10)); // Check every 10ms
    }
}

void initButtons(ButtonCallback onButtonPress, LongPressCallback onLongPress) {
    buttonPressCallback = onButtonPress;
    longPressCallback = onLongPress;

    // Configure all button pins as input with pull-up
    for (int i = 0; i < BTN_COUNT; i++) {
        gpio_config_t config = {
            .pin_bit_mask = (1ULL << buttonPins[i]),
            .mode = GPIO_MODE_INPUT,
            .pull_up_en = GPIO_PULLUP_ENABLE,
            .pull_down_en = GPIO_PULLDOWN_DISABLE,
            .intr_type = GPIO_INTR_DISABLE
        };
        gpio_config(&config);

        // Initialize state arrays
        buttonStates[i] = false;
        lastButtonStates[i] = false;
        buttonPressTime[i] = 0;
        longPressDetected[i] = false;
    }

    // Create button monitoring task
    xTaskCreatePinnedToCore(
        buttonMonitorTask,
        "ButtonMonitor",
        2048,
        nullptr,
        2,  // Higher priority than main loop
        &buttonTaskHandle,
        0   // Run on core 1
    );

    Serial.println("7-button joystick initialized");
}

void detachButtons() {
    if (buttonTaskHandle) {
        vTaskDelete(buttonTaskHandle);
        buttonTaskHandle = nullptr;
    }

    // Reset pin configurations
    for (int i = 0; i < BTN_COUNT; i++) {
        gpio_reset_pin(buttonPins[i]);
    }

    Serial.println("Buttons detached");
}
