#pragma once

#include <Arduino.h>
#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>
#include "../core/DeviceState.h"
#include "../audio/AudioManager.h"

// OLED display configuration
#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 32
#define OLED_RESET -1
#define SCREEN_ADDRESS 0x3C

// I2C pins for OLED
#define OLED_SDA_PIN 21
#define OLED_SCL_PIN 22

class DisplayManager {
public:
    DisplayManager();
    ~DisplayManager();
    
    // Initialize display
    bool init();
    
    // Main render function - call this from main loop
    void render();
    
    // Force display update
    void forceUpdate();
    
    // Display control
    void turnOn();
    void turnOff();
    void setBrightness(uint8_t brightness); // 0-255
    
    // Status display methods
    void showInitializing();
    void showSearchingTWS();
    void showConnecting();
    void showConnected(const String& deviceName, int fileCount);
    void showBrowsingFiles(const String& currentFile, int currentIndex, int totalFiles);
    void showPlayingState(const String& filename, int currentPos, int totalDuration, int volume);
    void showPausedState(const String& filename, int currentPos, int totalDuration, int volume);
    void showStoppedState(const String& filename, int volume);
    void showBusyOperation(BusyOperation operation);
    void showOff();
    
private:
    Adafruit_SSD1306* display;
    bool initialized;
    bool display_on;
    DeviceState lastState;
    unsigned long lastUpdate;
    static const unsigned long UPDATE_INTERVAL = 100; // Update every 100ms
    
    // Helper methods
    void clearDisplay();
    void drawProgressBar(int x, int y, int width, int height, int progress, int total);
    void drawVolumeBar(int x, int y, int width, int height, int volume);
    void drawCenteredText(const String& text, int y, int textSize = 1);
    void drawScrollingText(const String& text, int x, int y, int maxWidth, int textSize = 1);
    String formatTime(int seconds);
    String truncateString(const String& str, int maxChars);
    
    // Animation helpers
    unsigned long animationTimer;
    int animationFrame;
    void drawLoadingAnimation(int x, int y);
    void drawConnectingAnimation(int x, int y);
};

// Global display manager instance
extern DisplayManager displayManager;
