#include "DeviceState.h"
#include <Arduino.h>

// Global device state variables
volatile DeviceState currentState = INITIALIZING;
volatile bool deviceBusy = false;
volatile BusyOperation deviceBusyFor = BUSY_NONE;

// Global busy state functions
bool isBusy() {
    return deviceBusy;
}

BusyOperation getBusyOperation() {
    return deviceBusyFor;
}

void setBusy(BusyOperation operation) {
    deviceBusy = true;
    deviceBusyFor = operation;
    Serial.printf("[DEVICE] Setting busy state: %d\n", operation);
}

void clearBusy() {
    deviceBusy = false;
    deviceBusyFor = BUSY_NONE;
    Serial.println("[DEVICE] Clearing busy state");
}
