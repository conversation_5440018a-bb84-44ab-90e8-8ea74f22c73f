#include "CustomMP3Parser.h"

namespace CustomMP3Parser {

int parseBitrateFromFrame(const uint8_t *frame) {
  // Check frame sync: 11 bits should be all 1s
  if (frame[0] != 0xFF || (frame[1] & 0xE0) != 0xE0) {
    return 0;
  }

  int versionID = (frame[1] >> 3) & 0x03; // 2 bits
  int layerIndex = (frame[1] >> 1) & 0x03; // 2 bits
  int bitrateIndex = (frame[2] >> 4) & 0x0F; // 4 bits

  if (bitrateIndex == 0 || bitrateIndex == 0xF) {
    return 0; // Invalid bitrate index
  }

  // For simplicity, support MPEG Version 1 Layer III (common)
  // Add more tables if needed for V2 / V2.5 or other layers.
  static const int bitrates[16] = {
    0, 32, 40, 48, 56, 64, 80, 96,
    112, 128, 160, 192, 224, 256, 320, 0
  };

  int bitrateKbps = bitrates[bitrateIndex];

  return bitrateKbps * 1000; // Convert to bits per second
}

}
