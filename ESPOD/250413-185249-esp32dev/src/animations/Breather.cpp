#include "Breather.h"
#include <math.h>

#define FADE_DELAY 15         // ms between steps
#define MAX_BRIGHTNESS 127    // 50% of 255
#define STEPS 100             // Number of steps per fade

void breathe() {
  for (int i = 0; i <= STEPS; i++) {
    // Calculate sine wave value (0 to π)
    float angle = (PI * i) / STEPS;  // 0 to π
    // Sine curve: 0 -> 1 -> 0
    float brightness = sin(angle) * MAX_BRIGHTNESS;
    ledcWrite(LEDC_CHANNEL, (int)brightness);

    // ⏸️ Hold at peak
    if (i == STEPS / 2) {
      vTaskDelay(400 / portTICK_PERIOD_MS); // hold at max brightness
    }

    vTaskDelay(FADE_DELAY / portTICK_PERIOD_MS);
  }
}


void Breather(void *pvParameters) {
  // Setup PWM
  ledcSetup(LEDC_CHANNEL, LEDC_FREQ, LEDC_RES);
  ledcAttachPin(LED_PIN, LEDC_CHANNEL);

  while (true) {
    breathe();
    vTaskDelay(200 / portTICK_PERIOD_MS);  // Optional pause at min
  }
}

void initBreather() {
  xTaskCreatePinnedToCore(
    Breather,
    "Breather",
    2048,
    NULL,
    1,
    NULL,
    0
  );
}
