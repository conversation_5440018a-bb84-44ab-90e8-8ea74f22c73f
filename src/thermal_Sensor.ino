#include "wifi_manager.h"
#include "thermal_sensor.h"
#include "web_server.h"
#include "oled_display.h"

void setup() {
  Serial.begin(115200);
  initWiFi();
  initSensor();
  initDisplay();
  initWebServer();
}

void loop() {
  readPixels();
  String ipAddress = getWiFiIP();
  displayThermalData(pixels, maxTemp, minTemp, ipAddress.c_str());
  handleWebServer();
  delay(1000);  // Update display every second
}
