#include "thermal_sensor.h"
#include <Wire.h>

Adafruit_AMG88xx amg;
float pixels[AMG88xx_PIXEL_ARRAY_SIZE];
float maxTemp = 0.0;
float minTemp = 0.0;

void initSensor() {
  Wire.begin(9, 4);  // << 🟢 Correct placement here!
  
  if (!amg.begin()) {
    Serial.println("Could not find a valid AMG88xx sensor. Check wiring!");
    while (1);
  }
}


void readPixels() {
  amg.readPixels(pixels);
  calculateTempRange();
}

void calculateTempRange() {
  maxTemp = pixels[0];
  minTemp = pixels[0];

  for (int i = 1; i < AMG88xx_PIXEL_ARRAY_SIZE; i++) {
    if (pixels[i] > maxTemp) {
      maxTemp = pixels[i];
    }
    if (pixels[i] < minTemp) {
      minTemp = pixels[i];
    }
  }
}
