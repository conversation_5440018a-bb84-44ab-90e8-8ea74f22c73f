#include "oled_display.h"
#include "thermal_sensor.h"
#include <Wire.h>

// Use the same I2C bus but different address
Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET);

// Animation counter for dots
static int animationCounter = 0;

void initDisplay() {
  Serial.println("Initializing OLED display...");

  // The I2C bus is already initialized in thermal_sensor.cpp
  // Just initialize the OLED display on the existing bus
  if (!display.begin(SSD1306_SWITCHCAPVCC, SCREEN_ADDRESS)) {
    Serial.println(F("SSD1306 allocation failed"));
    // Try alternative I2C address
    if (!display.begin(SSD1306_SWITCHCAPVCC, 0x3D)) {
      Serial.println(F("SSD1306 allocation failed on both addresses"));
      return;
    } else {
      Serial.println("OLED found at address 0x3D");
    }
  } else {
    Serial.println("OLED found at address 0x3C");
  }
  
  // Clear the display buffer
  display.clearDisplay();
  
  // Set text properties
  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE);
  
  // Show initial message
  display.setCursor(0, 0);
  display.println(F("Thermal Sensor"));
  display.println(F("OLED Ready!"));
  display.display();
  
  delay(2000);
  display.clearDisplay();
}

void displayThermalData(float maxTemp, float minTemp, const char* url) {
  display.clearDisplay();

  // Convert Celsius to Fahrenheit
  float maxTempF = (maxTemp * 9.0 / 5.0) + 32.0;
  float minTempF = (minTemp * 9.0 / 5.0) + 32.0;

  // Display URL in top 1/3 portion with animation dots
  display.setCursor(0, 0);
  display.setTextSize(1);
  display.print(url);

  // Add animated dots to show activity
  animationCounter++;
  int numDots = (animationCounter / 10) % 4; // Change every 10 frames, cycle 0-3 dots
  for (int i = 0; i < numDots; i++) {
    display.print(".");
  }

  // Draw a line to separate URL from temperature data
  display.drawLine(0, 10, SCREEN_WIDTH, 10, SSD1306_WHITE);

  // Display temperatures side by side horizontally in the remaining 2/3 portion
  // Left side - Max temperature
  display.setCursor(0, 13);
  display.setTextSize(1);
  display.print(F("Max:"));
  display.setCursor(0, 22);
  display.setTextSize(2);  // Bigger font for temperature value
  display.print(maxTempF, 0);
  display.print(F("F"));

  // Right side - Min temperature
  display.setCursor(64, 13);  // Start at middle of screen (128/2 = 64)
  display.setTextSize(1);
  display.print(F("Min:"));
  display.setCursor(64, 22);
  display.setTextSize(2);  // Bigger font for temperature value
  display.print(minTempF, 0);
  display.print(F("F"));

  display.display();
}

void displayMessage(const char* message) {
  display.clearDisplay();
  display.setCursor(0, 0);
  display.setTextSize(1);
  display.println(message);
  display.display();
}

void clearDisplay() {
  display.clearDisplay();
  display.display();
}
