#include "oled_display.h"
#include "thermal_sensor.h"
#include <Wire.h>

// Use the same I2C bus but different address
Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET);

void initDisplay() {
  Serial.println("Initializing OLED display...");

  // The I2C bus is already initialized in thermal_sensor.cpp
  // Just initialize the OLED display on the existing bus
  if (!display.begin(SSD1306_SWITCHCAPVCC, SCREEN_ADDRESS)) {
    Serial.println(F("SSD1306 allocation failed"));
    // Try alternative I2C address
    if (!display.begin(SSD1306_SWITCHCAPVCC, 0x3D)) {
      Serial.println(F("SSD1306 allocation failed on both addresses"));
      return;
    } else {
      Serial.println("OLED found at address 0x3D");
    }
  } else {
    Serial.println("OLED found at address 0x3C");
  }
  
  // Clear the display buffer
  display.clearDisplay();
  
  // Set text properties
  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE);
  
  // Show initial message
  display.setCursor(0, 0);
  display.println(F("Thermal Sensor"));
  display.println(F("OLED Ready!"));
  display.display();
  
  delay(2000);
  display.clearDisplay();
}

void displayThermalData(float maxTemp, float minTemp, const char* url) {
  display.clearDisplay();

  // Convert Celsius to Fahrenheit
  float maxTempF = (maxTemp * 9.0 / 5.0) + 32.0;
  float minTempF = (minTemp * 9.0 / 5.0) + 32.0;

  // Display URL in top 1/3 portion (approximately 10-11 pixels height)
  display.setCursor(0, 0);
  display.setTextSize(1);
  display.println(url);

  // Draw a line to separate URL from temperature data
  display.drawLine(0, 10, SCREEN_WIDTH, 10, SSD1306_WHITE);

  // Display max temperature with bigger font in the remaining 2/3 portion
  display.setCursor(0, 13);
  display.setTextSize(2);  // Bigger font size
  display.print(F("Max:"));
  display.print(maxTempF, 0);  // No decimal places for cleaner look
  display.println(F("F"));

  // Display min temperature with bigger font
  display.setCursor(0, 23);  // Adjusted position for 32px height
  display.setTextSize(1);    // Slightly smaller to fit both lines
  display.print(F("Min: "));
  display.print(minTempF, 0);
  display.print(F("F"));

  display.display();
}

void displayMessage(const char* message) {
  display.clearDisplay();
  display.setCursor(0, 0);
  display.setTextSize(1);
  display.println(message);
  display.display();
}

void clearDisplay() {
  display.clearDisplay();
  display.display();
}
