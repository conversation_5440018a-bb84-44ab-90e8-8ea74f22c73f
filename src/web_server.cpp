#include <WebServer.h>
#include "thermal_sensor.h"
#include "web_server.h"

WebServer server(80);

void handleRoot() {
  String html = R"rawliteral(
  <!DOCTYPE html>
  <html>
  <head>
    <title>ESP32 Thermal Camera</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/heatmap.js/2.0.2/heatmap.min.js"></script>
    <style>
      body { background: #000; color: #fff; text-align: center; }
      #heatmapContainer { width: 256px; height: 256px; position: relative; display: inline-block; }
      #marker {
        position: absolute;
        width: 40px; height: 40px;
        border: 2px solid #fff; border-radius: 50%;
        pointer-events: none;
        transform: translate(-50%, -50%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 12px;
        background: rgba(0,0,0,0.3);
        z-index: 10;
      }
      canvas { border: 1px solid #555; margin-left: 20px; }
    </style>
  </head>
  <body>
    <h1>AMG8833 Thermal Camera</h1>
    <div style="display: flex; justify-content: center; align-items: center;">
      <div id="heatmapContainer">
        <div id="marker"></div>
      </div>
      <canvas id="colorbar" width="40" height="256"></canvas>
    </div>
    <script>
      const size = 8, C = 256;
      const hm = h337.create({
        container: document.getElementById('heatmapContainer'),
        radius: C / size * 1.5, blur: 0.85,
        gradient: { '.2': 'blue', '.4': 'cyan', '.6': 'lime', '.8': 'yellow', '1': 'red' }
      });
      const marker = document.getElementById('marker');
      const bar = document.getElementById('colorbar');
      const ctxB = bar.getContext('2d');

      function toF(c) {
        return c * 9 / 5 + 32;
      }

      function drawColorbar(minC, maxC) {
        const minF = toF(minC);
        const maxF = toF(maxC);

        const grad = ctxB.createLinearGradient(0, 0, 0, bar.height);
        grad.addColorStop(0, 'red');
        grad.addColorStop(0.2, 'yellow');
        grad.addColorStop(0.4, 'lime');
        grad.addColorStop(0.6, 'cyan');
        grad.addColorStop(1, 'blue');
        ctxB.fillStyle = grad;
        ctxB.fillRect(0, 0, bar.width, bar.height);

        ctxB.fillStyle = "#fff";
        ctxB.font = "12px sans-serif";
        ctxB.clearRect(0, 0, bar.width, bar.height);
        ctxB.fillStyle = grad;
        ctxB.fillRect(0, 0, bar.width, bar.height);
        ctxB.fillStyle = "#fff";
        ctxB.fillText(maxF.toFixed(1) + "\u00B0F", 2, 12);
        ctxB.fillText(minF.toFixed(1) + "\u00B0F", 2, bar.height - 4);
      }

      async function fetchPixels() {
        const res = await fetch('/pixels');
        const data = await res.json();
        const minC = Math.min(...data), maxC = Math.max(...data);
        const minF = toF(minC), maxF = toF(maxC);

        const pts = data.map((v, i) => ({
          x: (i % size + 0.5) * C / size,
          y: (Math.floor(i / size) + 0.5) * C / size,
          value: v
        }));

        hm.setData({ min: minC, max: maxC, data: pts });
        drawColorbar(minC, maxC);

        const idx = data.indexOf(maxC);
        const x = (idx % size + 0.5) * C / size;
        const y = (Math.floor(idx / size) + 0.5) * C / size;
        marker.style.left = `${x}px`;
        marker.style.top = `${y}px`;
        marker.innerText = `${maxF.toFixed(1)}\u00B0F`;
      }

      setInterval(fetchPixels, 100);
    </script>
  </body>
  </html>
  )rawliteral";

  server.send(200, "text/html", html);
}

void handlePixels() {
  readPixels();
  String json = "[";
  for (int i = 0; i < AMG88xx_PIXEL_ARRAY_SIZE; i++) {
    json += String(pixels[i], 2);
    if (i < AMG88xx_PIXEL_ARRAY_SIZE - 1) json += ",";
  }
  json += "]";
  server.send(200, "application/json", json);
}

void initWebServer() {
  server.on("/", handleRoot);
  server.on("/pixels", handlePixels);
  server.begin();
  Serial.println("HTTP server started");
}

void handleWebServer() {
  server.handleClient();
}
